import os
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    # Application settings
    DEBUG: bool = Field(default=True)
    HOST: str = Field(default="0.0.0.0")
    PORT: int = Field(default=8000)
    
    # Database settings
    DATABASE_URL: str = Field(default="sqlite:///./bot.db")
    
    # Security settings
    SECRET_KEY: str = Field(default="your-secret-key-here")
    ADMIN_USERNAME: str = Field(default="admin")
    ADMIN_PASSWORD: str = Field(default="password")
    
    # Telegram settings
    WEBHOOK_URL: str = Field(default="")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
