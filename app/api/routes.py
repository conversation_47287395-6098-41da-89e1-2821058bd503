from fastapi import APIRouter, Depends, HTTPException, status, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session
from typing import List
import json

from app.core.auth import authenticate_user, create_access_token, get_current_user
from app.core.config import settings
from app.db.database import get_db, engine, Base
from app.models.bot import Bot
from app.models.schemas import BotC<PERSON>, BotUpdate, BotInDB, Token
from app.bot.service import BotService
from app.bot.handler import BotHandler

# Create tables
Base.metadata.create_all(bind=engine)

# Initialize router
router = APIRouter()

# Initialize templates
templates = Jinja2Templates(directory="app/templates")

# Authentication routes
@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = create_access_token(data={"sub": form_data.username})

    return {"access_token": access_token, "token_type": "bearer"}

# Admin panel routes
@router.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@router.get("/admin", response_class=HTMLResponse)
async def admin_panel(request: Request, db: Session = Depends(get_db)):
    try:
        current_user = await get_current_user(request=request)
        bots = BotService.get_bots(db)
        return templates.TemplateResponse("admin.html", {"request": request, "bots": bots})
    except HTTPException:
        return RedirectResponse(url="/admin/login", status_code=status.HTTP_303_SEE_OTHER)

@router.get("/admin/login", response_class=HTMLResponse)
async def admin_login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@router.post("/admin/login")
async def admin_login(request: Request, username: str = Form(...), password: str = Form(...)):
    # Debug print
    print(f"Login attempt - Username: {username}, Password: {password}")
    print(f"Expected - Username: {settings.ADMIN_USERNAME}, Password: {settings.ADMIN_PASSWORD}")

    user = authenticate_user(username, password)

    if not user:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "error": "Invalid username or password"
        })

    access_token = create_access_token(data={"sub": username})
    response = RedirectResponse(url="/admin", status_code=status.HTTP_303_SEE_OTHER)
    response.set_cookie(key="access_token", value=f"Bearer {access_token}", httponly=True)

    return response

@router.get("/admin/logout")
async def admin_logout():
    response = RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)
    response.delete_cookie(key="access_token")

    return response

# Bot management routes
@router.post("/admin/bots", response_model=BotInDB)
async def create_bot(
    request: Request,
    bot_create: BotCreate,
    db: Session = Depends(get_db)
):
    try:
        # Authenticate user
        print(f"Authenticating user for bot creation")
        current_user = await get_current_user(request=request)
        print(f"User authenticated: {current_user}")

        # Check if bot already exists
        print(f"Checking if bot with token {bot_create.token[:5]}...{bot_create.token[-5:]} exists")
        db_bot = BotService.get_bot_by_token(db, bot_create.token)

        if db_bot:
            print(f"Bot already registered")
            raise HTTPException(status_code=400, detail="Bot already registered")

        # Create bot
        print(f"Creating bot with token {bot_create.token[:5]}...{bot_create.token[-5:]}")
        bot = BotService.create_bot(db, bot_create)

        if not bot:
            print(f"Invalid bot token")
            raise HTTPException(status_code=400, detail="Invalid bot token")

        print(f"Bot created successfully: {bot.name}")

        # Set webhook
        webhook_url = f"{settings.WEBHOOK_URL}/webhook/{bot.token}"
        print(f"Setting webhook URL: {webhook_url}")
        webhook_success = BotService.set_webhook(bot.token, webhook_url)
        print(f"Webhook set success: {webhook_success}")

        return bot
    except Exception as e:
        print(f"Error creating bot: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating bot: {str(e)}")

@router.get("/admin/bots", response_model=List[BotInDB])
async def read_bots(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    # Authenticate user
    await get_current_user(request=request)
    bots = BotService.get_bots(db, skip=skip, limit=limit)
    return bots

@router.get("/admin/bots/{bot_id}", response_model=BotInDB)
async def read_bot(
    request: Request,
    bot_id: int,
    db: Session = Depends(get_db)
):
    # Authenticate user
    await get_current_user(request=request)
    bot = BotService.get_bot(db, bot_id)

    if not bot:
        raise HTTPException(status_code=404, detail="Bot not found")

    return bot

@router.put("/admin/bots/{bot_id}", response_model=BotInDB)
async def update_bot(
    request: Request,
    bot_id: int,
    bot_update: BotUpdate,
    db: Session = Depends(get_db)
):
    # Authenticate user
    await get_current_user(request=request)
    bot = BotService.update_bot(db, bot_id, bot_update)

    if not bot:
        raise HTTPException(status_code=404, detail="Bot not found or invalid token")

    # Update webhook if token changed or is_active changed
    if bot_update.token or bot_update.is_active is not None:
        if bot.is_active:
            webhook_url = f"{settings.WEBHOOK_URL}/webhook/{bot.token}"
            BotService.set_webhook(bot.token, webhook_url)
        else:
            # Disable webhook instead of deleting it when bot is turned off
            BotService.disable_webhook(bot.token)

    return bot

@router.delete("/admin/bots/{bot_id}")
async def delete_bot(
    request: Request,
    bot_id: int,
    db: Session = Depends(get_db)
):
    # Authenticate user
    await get_current_user(request=request)
    # Get bot before deletion to delete webhook
    bot = BotService.get_bot(db, bot_id)

    if not bot:
        raise HTTPException(status_code=404, detail="Bot not found")

    # Delete webhook when bot is being deleted
    BotService.delete_webhook(bot.token)

    # Delete bot from database
    success = BotService.delete_bot(db, bot_id)

    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete bot")

    return {"status": "success", "message": "Bot deleted"}

# Webhook management routes
@router.post("/admin/bots/{bot_id}/enable-webhook")
async def enable_bot_webhook(
    request: Request,
    bot_id: int,
    db: Session = Depends(get_db)
):
    try:
        # Authenticate user
        print(f"Authenticating user for enabling webhook")
        current_user = await get_current_user(request=request)
        print(f"User authenticated: {current_user}")

        # Get bot
        bot = BotService.get_bot(db, bot_id)

        if not bot:
            raise HTTPException(status_code=404, detail="Bot not found")

        # Enable webhook
        webhook_url = f"{settings.WEBHOOK_URL}/webhook/{bot.token}"
        print(f"Enabling webhook URL: {webhook_url}")
        webhook_success = BotService.enable_webhook(bot.token, webhook_url)
        print(f"Webhook enable success: {webhook_success}")

        if not webhook_success:
            raise HTTPException(status_code=500, detail="Failed to enable webhook")

        # Update bot status
        bot_update = BotUpdate(is_active=True)
        updated_bot = BotService.update_bot(db, bot_id, bot_update)

        return {"status": "success", "message": "Webhook enabled", "bot": updated_bot}
    except Exception as e:
        print(f"Error enabling webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error enabling webhook: {str(e)}")

@router.post("/admin/bots/{bot_id}/disable-webhook")
async def disable_bot_webhook(
    request: Request,
    bot_id: int,
    db: Session = Depends(get_db)
):
    try:
        # Authenticate user
        print(f"Authenticating user for disabling webhook")
        current_user = await get_current_user(request=request)
        print(f"User authenticated: {current_user}")

        # Get bot
        bot = BotService.get_bot(db, bot_id)

        if not bot:
            raise HTTPException(status_code=404, detail="Bot not found")

        # Disable webhook
        print(f"Disabling webhook for bot: {bot.name}")
        webhook_success = BotService.disable_webhook(bot.token)
        print(f"Webhook disable success: {webhook_success}")

        if not webhook_success:
            raise HTTPException(status_code=500, detail="Failed to disable webhook")

        # Update bot status
        bot_update = BotUpdate(is_active=False)
        updated_bot = BotService.update_bot(db, bot_id, bot_update)

        return {"status": "success", "message": "Webhook disabled", "bot": updated_bot}
    except Exception as e:
        print(f"Error disabling webhook: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error disabling webhook: {str(e)}")

# Webhook route
@router.post("/webhook/{token}")
async def webhook(token: str, request: Request, db: Session = Depends(get_db)):
    # Parse update from Telegram
    update = await request.json()

    # Handle update
    result = BotHandler.handle_update(db, token, update)

    return result
