import requests
from sqlalchemy.orm import Session
from app.models.bot import <PERSON><PERSON>
from app.models.schemas import BotCreate, BotUpdate

class BotService:
    @staticmethod
    def get_bot_info(token: str):
        """Get bot information using Telegram Bot API"""
        url = f"https://api.telegram.org/bot{token}/getMe"
        print(f"Requesting bot info from: {url}")
        try:
            response = requests.get(url)
            print(f"Response status: {response.status_code}")
            print(f"Response content: {response.text}")

            if response.status_code != 200:
                return None

            data = response.json()
            if not data.get("ok"):
                return None

            return data.get("result")
        except Exception as e:
            print(f"Error getting bot info: {str(e)}")
            return None

    @staticmethod
    def set_webhook(token: str, webhook_url: str):
        """Set webhook for the bot"""
        url = f"https://api.telegram.org/bot{token}/setWebhook"
        params = {"url": webhook_url}
        print(f"Setting webhook: {url} with params: {params}")
        try:
            response = requests.get(url, params=params, timeout=10)
            print(f"Webhook response status: {response.status_code}")
            print(f"Webhook response content: {response.text}")

            if response.status_code != 200:
                return False

            data = response.json()
            return data.get("ok", False)
        except Exception as e:
            print(f"Error setting webhook: {str(e)}")
            return False

    @staticmethod
    def enable_webhook(token: str, webhook_url: str):
        """Enable webhook for the bot"""
        return BotService.set_webhook(token, webhook_url)

    @staticmethod
    def disable_webhook(token: str):
        """Disable webhook for the bot without deleting it"""
        url = f"https://api.telegram.org/bot{token}/setWebhook"
        params = {"url": ""}
        print(f"Disabling webhook: {url}")
        try:
            response = requests.get(url, params=params, timeout=10)
            print(f"Disable webhook response status: {response.status_code}")
            print(f"Disable webhook response content: {response.text}")

            if response.status_code != 200:
                return False

            data = response.json()
            return data.get("ok", False)
        except Exception as e:
            print(f"Error disabling webhook: {str(e)}")
            return False

    @staticmethod
    def delete_webhook(token: str):
        """Delete webhook for the bot"""
        url = f"https://api.telegram.org/bot{token}/deleteWebhook"
        print(f"Deleting webhook: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"Delete webhook response status: {response.status_code}")
            print(f"Delete webhook response content: {response.text}")

            if response.status_code != 200:
                return False

            data = response.json()
            return data.get("ok", False)
        except Exception as e:
            print(f"Error deleting webhook: {str(e)}")
            return False

    @staticmethod
    def create_bot(db: Session, bot_create: BotCreate):
        """Create a new bot in the database"""
        # Get bot info from Telegram
        bot_info = BotService.get_bot_info(bot_create.token)

        if not bot_info:
            return None

        # Create bot in database
        db_bot = Bot(
            token=bot_create.token,
            name=bot_info.get("first_name", "Unknown"),
            is_active=bot_create.is_active
        )

        db.add(db_bot)
        db.commit()
        db.refresh(db_bot)

        return db_bot

    @staticmethod
    def get_bots(db: Session, skip: int = 0, limit: int = 100):
        """Get all bots from the database"""
        return db.query(Bot).offset(skip).limit(limit).all()

    @staticmethod
    def get_bot(db: Session, bot_id: int):
        """Get a bot by ID"""
        return db.query(Bot).filter(Bot.id == bot_id).first()

    @staticmethod
    def get_bot_by_token(db: Session, token: str):
        """Get a bot by token"""
        return db.query(Bot).filter(Bot.token == token).first()

    @staticmethod
    def update_bot(db: Session, bot_id: int, bot_update: BotUpdate):
        """Update a bot in the database"""
        db_bot = BotService.get_bot(db, bot_id)

        if not db_bot:
            return None

        # Update fields if provided
        update_data = bot_update.dict(exclude_unset=True)

        # If token is updated, get new bot info
        if "token" in update_data and update_data["token"] != db_bot.token:
            bot_info = BotService.get_bot_info(update_data["token"])

            if not bot_info:
                return None

            update_data["name"] = bot_info.get("first_name", "Unknown")

        for key, value in update_data.items():
            setattr(db_bot, key, value)

        db.commit()
        db.refresh(db_bot)

        return db_bot

    @staticmethod
    def delete_bot(db: Session, bot_id: int):
        """Delete a bot from the database"""
        db_bot = BotService.get_bot(db, bot_id)

        if not db_bot:
            return False

        db.delete(db_bot)
        db.commit()

        return True
