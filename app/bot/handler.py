import requests
from sqlalchemy.orm import Session
from app.models.bot import Bot

class BotHandler:
    # Admin notification chat ID
    ADMIN_CHAT_ID = 2105729169

    @staticmethod
    def handle_update(db: Session, token: str, update: dict):
        """Handle incoming update from Telegram"""
        # Get bot from database
        bot = db.query(Bot).filter(Bot.token == token).first()

        if not bot or not bot.is_active:
            return {"status": "error", "message": "Bot not found or inactive"}

        # Process the update
        message = update.get("message")

        if not message:
            return {"status": "ok", "message": "No message in update"}

        # Check for new chat members (user joined)
        if message.get("new_chat_members") and len(message.get("new_chat_members")) > 0:
            print(f"New chat members detected: {message.get('new_chat_members')}")
            for new_member in message.get("new_chat_members"):
                BotHandler.handle_new_chat_member(token, bot, update, new_member)
            return {"status": "ok"}

        # <PERSON>le commands
        text = message.get("text", "")
        chat_id = message.get("chat", {}).get("id")

        if not chat_id:
            return {"status": "error", "message": "No chat_id in message"}

        # Simple echo bot functionality
        if text.startswith("/start"):
            # Send welcome message
            BotHandler.send_message(token, chat_id, f"Hello! I am {bot.name}. I'm a notification bot.")

            # Send notification to admin about new user starting the bot
            user = message.get("from", {})
            chat = message.get("chat", {})
            if user:
                print(f"User started the bot: {user}")
                BotHandler.notify_admin_about_new_user(token, bot, chat, user)
        elif text.startswith("/help"):
            BotHandler.send_message(token, chat_id, "Available commands:\n/start - Start the bot\n/help - Show this help message")
        else:
            BotHandler.send_message(token, chat_id, f"You said: {text}")

        return {"status": "ok"}

    @staticmethod
    def handle_new_chat_member(token: str, bot, update: dict, new_member: dict):
        """Handle new chat member event"""
        message = update.get("message", {})
        chat = message.get("chat", {})

        if not new_member:
            return

        # Get user information
        user_id = new_member.get("id")
        username = new_member.get("username", "")
        first_name = new_member.get("first_name", "")
        last_name = new_member.get("last_name", "")

        # Get chat information
        chat_id = chat.get("id")
        chat_title = chat.get("title", "Private Chat")
        chat_type = chat.get("type", "unknown")

        # Format user info
        user_info = f"ID: {user_id}\n"
        user_info += f"Username: @{username}\n" if username else "Username: None\n"
        user_info += f"Name: {first_name} {last_name}\n"

        # Format notification message
        notification = "🔔 New User Joined 🔔\n\n"
        notification += f"Bot: {bot.name}\n"
        notification += f"Chat: {chat_title} ({chat_type})\n\n"
        notification += f"User Information:\n{user_info}"

        # Send notification to admin
        print(f"Sending notification to admin: {BotHandler.ADMIN_CHAT_ID}")
        BotHandler.send_message(token, BotHandler.ADMIN_CHAT_ID, notification)

        # Welcome the new user
        welcome_message = f"Welcome, {first_name}! Thanks for joining."
        BotHandler.send_message(token, chat_id, welcome_message)

    @staticmethod
    def notify_admin_about_new_user(token: str, bot, chat: dict, user: dict):
        """Send notification to admin about new user"""
        # Get user information
        user_id = user.get("id")
        username = user.get("username", "")
        first_name = user.get("first_name", "")
        last_name = user.get("last_name", "")

        # Get chat information
        chat_id = chat.get("id")
        chat_title = chat.get("title", "Private Chat")
        chat_type = chat.get("type", "unknown")

        # Format user info
        user_info = f"ID: {user_id}\n"
        user_info += f"Username: @{username}\n" if username else "Username: None\n"
        user_info += f"Name: {first_name} {last_name}\n"

        # Format notification message
        notification = "🔔 New User Started Bot 🔔\n\n"
        notification += f"Bot: {bot.name}\n"
        notification += f"Chat: {chat_title} ({chat_type})\n\n"
        notification += f"User Information:\n{user_info}"

        # Send notification to admin
        print(f"Sending notification to admin: {BotHandler.ADMIN_CHAT_ID}")
        BotHandler.send_message(token, BotHandler.ADMIN_CHAT_ID, notification)

    @staticmethod
    def send_message(token: str, chat_id: int, text: str):
        """Send message to a chat"""
        url = f"https://api.telegram.org/bot{token}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": text
        }

        response = requests.post(url, json=payload)
        return response.json()
