<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Notification - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
        }
        .main-content {
            margin-top: 56px;
            margin-left: 200px;
            padding: 2rem;
        }
        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                position: relative;
                padding-top: 0;
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Bot Notification</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/logout">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin">
                                <i class="bi bi-robot"></i> Bots
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Bots</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBotModal">
                        <i class="bi bi-plus"></i> Add Bot
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Token</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bot in bots %}
                            <tr>
                                <td>{{ bot.id }}</td>
                                <td>{{ bot.name }}</td>
                                <td>{{ bot.token[:10] }}...{{ bot.token[-10:] }}</td>
                                <td>
                                    {% if bot.is_active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ bot.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary edit-bot" data-id="{{ bot.id }}" data-token="{{ bot.token }}" data-active="{{ bot.is_active }}">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    {% if bot.is_active %}
                                    <button class="btn btn-sm btn-outline-warning disable-webhook" data-id="{{ bot.id }}" data-name="{{ bot.name }}">
                                        <i class="bi bi-pause-fill"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-outline-success enable-webhook" data-id="{{ bot.id }}" data-name="{{ bot.name }}">
                                        <i class="bi bi-play-fill"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-danger delete-bot" data-id="{{ bot.id }}" data-name="{{ bot.name }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">No bots found. Add a new bot to get started.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Bot Modal -->
    <div class="modal fade" id="addBotModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Bot</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addBotForm">
                        <div class="mb-3">
                            <label for="botToken" class="form-label">Bot Token</label>
                            <input type="text" class="form-control" id="botToken" required>
                            <div class="form-text">Enter the token provided by BotFather.</div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="botActive" checked>
                            <label class="form-check-label" for="botActive">Active</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="addBotBtn">Add Bot</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Bot Modal -->
    <div class="modal fade" id="editBotModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Bot</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editBotForm">
                        <input type="hidden" id="editBotId">
                        <div class="mb-3">
                            <label for="editBotToken" class="form-label">Bot Token</label>
                            <input type="text" class="form-control" id="editBotToken" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="editBotActive">
                            <label class="form-check-label" for="editBotActive">Active</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="updateBotBtn">Update Bot</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Bot Modal -->
    <div class="modal fade" id="deleteBotModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Delete Bot</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the bot <strong id="deleteBotName"></strong>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                    <input type="hidden" id="deleteBotId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add Bot
            document.getElementById('addBotBtn').addEventListener('click', function() {
                const token = document.getElementById('botToken').value;
                const isActive = document.getElementById('botActive').checked;

                if (!token) {
                    alert('Please enter a bot token');
                    return;
                }

                fetch('/admin/bots', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: token,
                        is_active: isActive
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Failed to add bot');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    alert('Bot added successfully!');
                    location.reload();
                })
                .catch(error => {
                    alert(error.message);
                });
            });

            // Edit Bot
            document.querySelectorAll('.edit-bot').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const token = this.getAttribute('data-token');
                    const isActive = this.getAttribute('data-active') === 'True';

                    document.getElementById('editBotId').value = id;
                    document.getElementById('editBotToken').value = token;
                    document.getElementById('editBotActive').checked = isActive;

                    const editModal = new bootstrap.Modal(document.getElementById('editBotModal'));
                    editModal.show();
                });
            });

            // Update Bot
            document.getElementById('updateBotBtn').addEventListener('click', function() {
                const id = document.getElementById('editBotId').value;
                const token = document.getElementById('editBotToken').value;
                const isActive = document.getElementById('editBotActive').checked;

                if (!token) {
                    alert('Please enter a bot token');
                    return;
                }

                fetch(`/admin/bots/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: token,
                        is_active: isActive
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Failed to update bot');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    alert('Bot updated successfully!');
                    location.reload();
                })
                .catch(error => {
                    alert(error.message);
                });
            });

            // Delete Bot
            document.querySelectorAll('.delete-bot').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');

                    document.getElementById('deleteBotId').value = id;
                    document.getElementById('deleteBotName').textContent = name;

                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteBotModal'));
                    deleteModal.show();
                });
            });

            // Confirm Delete
            document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
                const id = document.getElementById('deleteBotId').value;

                fetch(`/admin/bots/${id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Failed to delete bot');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    alert('Bot deleted successfully!');
                    location.reload();
                })
                .catch(error => {
                    alert(error.message);
                });
            });

            // Enable Webhook
            document.querySelectorAll('.enable-webhook').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');

                    if (confirm(`Are you sure you want to enable webhook for bot "${name}"?`)) {
                        fetch(`/admin/bots/${id}/enable-webhook`, {
                            method: 'POST'
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.json().then(data => {
                                    throw new Error(data.detail || 'Failed to enable webhook');
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            alert('Webhook enabled successfully!');
                            location.reload();
                        })
                        .catch(error => {
                            alert(error.message);
                        });
                    }
                });
            });

            // Disable Webhook
            document.querySelectorAll('.disable-webhook').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');

                    if (confirm(`Are you sure you want to disable webhook for bot "${name}"?`)) {
                        fetch(`/admin/bots/${id}/disable-webhook`, {
                            method: 'POST'
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.json().then(data => {
                                    throw new Error(data.detail || 'Failed to disable webhook');
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            alert('Webhook disabled successfully!');
                            location.reload();
                        })
                        .catch(error => {
                            alert(error.message);
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
