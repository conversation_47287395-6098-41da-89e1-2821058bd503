from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

# Bot schemas
class BotBase(BaseModel):
    token: str
    is_active: bool = True

class BotCreate(BotBase):
    pass

class BotUpdate(BaseModel):
    token: Optional[str] = None
    name: Optional[str] = None
    is_active: Optional[bool] = None

class BotInDB(BotBase):
    id: int
    name: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str
