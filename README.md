# Bot Notification

A simple bot management system with a secured admin panel, built with FastAPI and webhooks.

## Features

- Secure admin panel with login
- Add, edit, and delete Telegram bots
- Automatic bot name retrieval using Telegram Bot API
- Webhook-based bot communication
- Simple bot functionality (echo bot)

## Requirements

- Python 3.8+
- FastAPI
- SQLAlchemy
- Jinja2 Templates
- Telegram Bot API token(s)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/bot-notification.git
cd bot-notification
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure the application:
   - Edit the `.env` file with your settings
   - Set the `WEBHOOK_URL` to your public URL where the bot can receive webhooks

5. Run the application:
```bash
python main.py
```

## Usage

1. Access the admin panel at `http://localhost:8000/`
2. Login with the credentials set in the `.env` file (default: admin/password)
3. Add a new bot by providing a valid Telegram Bot API token
4. The bot will automatically be configured to use webhooks

## Deployment

For production deployment:

1. Set `DEBUG=False` in the `.env` file
2. Use a production ASGI server like Gunicorn:
```bash
gunicorn -k uvicorn.workers.UvicornWorker main:app
```

3. Set up a reverse proxy (Nginx, Apache) to handle HTTPS
4. Make sure your server is accessible from the internet for webhooks to work

## Security Considerations

- Change the default admin credentials
- Use a strong `SECRET_KEY`
- In production, always use HTTPS
- Consider implementing rate limiting and additional security measures

## License

MIT
