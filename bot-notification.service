[Unit]
Description=Bot Notification Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/root/apps/bot-notification
Environment=PATH=/root/apps/bot-notification/venv/bin
ExecStart=/root/apps/bot-notification/venv/bin/python main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=bot-notification

# Environment variables
Environment=DEBUG=False
Environment=HOST=127.0.0.1
Environment=PORT=8000
Environment=WEBHOOK_URL=https://channel.demo-projects.uz

[Install]
WantedBy=multi-user.target
