#!/bin/bash

# Bot Notification Service Deployment Script
# This script sets up the bot-notification service and nginx configuration

set -e

echo "🚀 Starting deployment of Bot Notification Service..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Variables
SERVICE_NAME="bot-notification"
APP_DIR="/root/apps/bot-notification"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
SYSTEMD_DIR="/etc/systemd/system"

echo "📁 Setting up directories and permissions..."

# Create www-data user if it doesn't exist
if ! id "www-data" &>/dev/null; then
    useradd -r -s /bin/false www-data
fi

# Set proper ownership
chown -R www-data:www-data "$APP_DIR"
chmod +x "$APP_DIR/deploy.sh"

echo "🔧 Installing system service..."

# Copy service file to systemd directory
cp "$APP_DIR/$SERVICE_NAME.service" "$SYSTEMD_DIR/"

# Reload systemd and enable service
systemctl daemon-reload
systemctl enable "$SERVICE_NAME"

echo "🌐 Configuring nginx..."

# Copy nginx configuration
cp "$APP_DIR/nginx.conf" "$NGINX_SITES_AVAILABLE/channel.demo-projects.uz"

# Enable the site
ln -sf "$NGINX_SITES_AVAILABLE/channel.demo-projects.uz" "$NGINX_SITES_ENABLED/"

# Remove default nginx site if it exists
if [ -f "$NGINX_SITES_ENABLED/default" ]; then
    rm "$NGINX_SITES_ENABLED/default"
fi

# Test nginx configuration
nginx -t

echo "🔄 Starting services..."

# Start the bot notification service
systemctl start "$SERVICE_NAME"

# Reload nginx
systemctl reload nginx

echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Service Status:"
systemctl status "$SERVICE_NAME" --no-pager -l
echo ""
echo "🌐 Nginx Status:"
systemctl status nginx --no-pager -l
echo ""
echo "🔗 Your service should now be available at:"
echo "   http://channel.demo-projects.uz"
echo ""
echo "📝 Useful commands:"
echo "   View logs: journalctl -u $SERVICE_NAME -f"
echo "   Restart service: systemctl restart $SERVICE_NAME"
echo "   Check nginx: nginx -t"
echo "   Reload nginx: systemctl reload nginx"
